package com.jp.med.cms.modules.contractManage.service.write.impl;

import com.jp.med.cms.modules.contractManage.dto.CmsContractDetailDto;
import com.jp.med.cms.modules.contractManage.dto.CmsContractDto;
import com.jp.med.cms.modules.contractManage.mapper.read.CmsContractDetailReadMapper;
import com.jp.med.cms.modules.contractManage.mapper.read.CmsContractReadMapper;
import com.jp.med.cms.modules.contractManage.mapper.read.CmsPaymentTermsReadMapper;
import com.jp.med.cms.modules.contractManage.vo.CmsContractDetailVo;
import com.jp.med.cms.modules.contractManage.vo.CmsContractVo;
import com.jp.med.cms.modules.contractManage.vo.CmsPaymentTermsVo;
import com.jp.med.cms.modules.contractType.dto.CmsContractTypeDto;
import com.jp.med.cms.modules.contractType.mapper.read.CmsContractTypeReadMapper;
import com.jp.med.cms.modules.contractType.vo.CmsContractTypeVo;
import com.jp.med.cms.modules.opposite.dto.CmsContractOppositeDto;
import com.jp.med.cms.modules.opposite.dto.CmsOppositeContactsDto;
import com.jp.med.cms.modules.opposite.mapper.read.CmsContractOppositeReadMapper;
import com.jp.med.cms.modules.opposite.vo.CmsContractOppositeVo;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.dto.ecs.EcsReimContractTask;
import com.jp.med.common.dto.ecs.EcsReimContractTaskDetail;
import com.jp.med.common.dto.ecs.EcsReimPurcTaskDetail;
import com.jp.med.common.entity.user.HrmUser;
import com.jp.med.common.feign.ecs.EcsReimFeignService;
import com.jp.med.common.util.DateUtil;
import com.jp.med.common.util.FeignExecuteUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.cms.modules.contractManage.mapper.write.CmsPaymentTermsWriteMapper;
import com.jp.med.cms.modules.contractManage.dto.CmsPaymentTermsDto;
import com.jp.med.cms.modules.contractManage.service.write.CmsPaymentTermsWriteService;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 付款条件
 * 
 * <AUTHOR>
 * @email -
 * @date 2024-06-20 11:31:27
 */
@Service
@Transactional(readOnly = false)
public class CmsPaymentTermsWriteServiceImpl extends ServiceImpl<CmsPaymentTermsWriteMapper, CmsPaymentTermsDto>
		implements CmsPaymentTermsWriteService {

	@Autowired
	private CmsPaymentTermsWriteMapper paymentTermsWriteMapper;

	@Autowired
	private CmsPaymentTermsReadMapper payTermsReadMapper;

	@Autowired
	private CmsContractDetailReadMapper contractDetailsReadMapper;

	@Autowired
	private CmsContractReadMapper contractReadMapper;

	@Autowired
	private CmsContractOppositeReadMapper oppositeReadMapper;

	@Autowired
	private CmsContractTypeReadMapper contractTypeReadMapper;

	@Autowired
	private EcsReimFeignService ecsReimFeignService;

	@Override
	public void goGenerateReim(CmsPaymentTermsDto dto) {
		HrmUser hrmUser = dto.getSysUser().getHrmUser();
		String appyer = StringUtils.isNotEmpty(hrmUser.getEmpCode()) ? hrmUser.getEmpCode()
				: dto.getSysUser().getUsername();
		String appyerDept = StringUtils.isNotEmpty(hrmUser.getHrmOrgId()) ? hrmUser.getHrmOrgId()
				: dto.getSysUser().getSysOrgId();

		// 创建时间
		String currentTime = DateUtil.getCurrentTime(null);
		List<CmsPaymentTermsVo> cmsPaymentTermsVos = payTermsReadMapper.queryList(dto);
		CmsPaymentTermsVo payVo = cmsPaymentTermsVos.get(0);
		EcsReimContractTask task = new EcsReimContractTask();
		// 封装数据
		// 合同id，后期用来跟踪合同，以及付款计划属于第几期
		task.setContractId(payVo.getContractId());
		// 设置档期付款计划id，用来跟踪详情
		task.setPaymentId(dto.getId());
		// 合同总额(这只是展示的不是当前要报销的金额)
		task.setTotalAmt(payVo.getTotalAmt());
		task.setStage(payVo.getStage());
		task.setProportion(payVo.getProportion());
		task.setPaymentTime(payVo.getPaymentTime());

		// 写状态
		/**
		 * 0：已生成计划未付款，
		 * 1：已生成待申请报销，
		 * 2：已提交报销申请，
		 * 3：报销通过，
		 * 4：已生成凭证(已付款)
		 */
		dto.setBusinessStatus(MedConst.TYPE_1);
		dto.setUpdateTime(currentTime);
		dto.setUpdtr(appyer);
		// 添加推送任务时间记录
		dto.setPushTaskTime(currentTime);
		paymentTermsWriteMapper.updateById(dto);

		// 写一些合同内容进去
		CmsContractDto cmsContractDto = new CmsContractDto();
		cmsContractDto.setId(payVo.getContractId());
		// 查合同内容
		List<CmsContractVo> cmsContractVos = contractReadMapper.queryList(cmsContractDto);
		CmsContractVo contractVo = cmsContractVos.get(0);
		task.setCtCode(contractVo.getCtCode());
		task.setCtName(contractVo.getCtName());
		task.setCtUnifiedCode(contractVo.getCtUnifiedCode());
		task.setTypeCode(contractVo.getCtTypeCode());
		task.setBank(contractVo.getOppositeBank());
		task.setBankcode(contractVo.getOppositeAccount());
		task.setAcctname(contractVo.getOppositeName());
		task.setPaymentType(contractVo.getPaymentType());

		// 查乙方
		CmsContractOppositeDto oppositeDto = new CmsContractOppositeDto();
		oppositeDto.setId(contractVo.getOppositeId());
		List<CmsContractOppositeVo> cmsContractOppositeVos = oppositeReadMapper.queryList(oppositeDto);
		CmsContractOppositeVo oppositeVo = cmsContractOppositeVos.get(0);
		// 写一下合同乙方信息
		task.setOppositeName(oppositeVo.getOppositeName());
		// task.
		// 开户行

		// 申请人信息
		task.setAppyer(appyer);
		task.setAppyerDept(appyerDept);

		CmsContractDetailDto detailDto = new CmsContractDetailDto();
		detailDto.setApplyId(payVo.getContractId());

		List<CmsContractDetailVo> cmsContractDetailVos = contractDetailsReadMapper.queryList(detailDto);

		// 处理多个附件，用逗号拼接
		StringBuilder attBuilder = new StringBuilder();
		StringBuilder attNameBuilder = new StringBuilder();

		for (int i = 0; i < cmsContractDetailVos.size(); i++) {
			CmsContractDetailVo detailVo = cmsContractDetailVos.get(i);
			if (i > 0) {
				attBuilder.append(",");
				attNameBuilder.append(",");
			}
			attBuilder.append(detailVo.getAtt());
			attNameBuilder.append(detailVo.getAttName());
		}

		task.setContractAtt(attBuilder.toString());
		task.setContractAttName(attNameBuilder.toString());

		// 报销详情
		EcsReimContractTaskDetail taskDetail = new EcsReimContractTaskDetail();

		// 查询合同类型
		CmsContractTypeDto typeDto = new CmsContractTypeDto();
		typeDto.setCode(contractVo.getCtTypeCode());
		List<CmsContractTypeVo> typeVos = contractTypeReadMapper.queryList(typeDto);
		CmsContractTypeVo typeVo = typeVos.get(0);

		// 报销摘要 : 摘要格式 [合同类型]-合同名称+"报销"
		StringBuilder sb = new StringBuilder("【");
		sb.append(typeVo.getName()).append("】- ").append(contractVo.getCtName());
		taskDetail.setReimAbst(sb.toString());
		// 报销科室
		taskDetail.setOrgId(appyerDept);

		// 🆕 设置使用科室
		if (StringUtils.isNotEmpty(contractVo.getUseOrg())) {
			task.setUseOrg(contractVo.getUseOrg());
		}

		// 查询是否该合同分类绑定了报销类型一个分类对应一个报销类型
		if (StringUtils.isNotEmpty(typeVo.getReimType())) {
			taskDetail.setReimType(typeVo.getReimType());
		}
		// 当期付款计划金额
		taskDetail.setReimAmt(payVo.getCurrentPayAmt());
		// 发票。看后期怎么设计
		// taskDetail.setAtt();

		List<EcsReimContractTaskDetail> details = new ArrayList<>();
		details.add(taskDetail);
		task.setDetails(details);

		// 设置需要报销的金额，来源于taskDetail的ReimAmt
		task.setNeedReimAmt(taskDetail.getReimAmt());

		// Feign调用写入数据
		FeignExecuteUtil.execute(ecsReimFeignService.saveContractTask(task));

	}

	@Override
	public void updateReimburseInfo(Integer paymentId, Integer reimId, String reimbursePerson, String reimburseDept,
			String reimburseTime, String reimburseNo) {
		CmsPaymentTermsDto dto = new CmsPaymentTermsDto();
		dto.setId(paymentId);
		dto.setReimId(reimId);
		if (StringUtils.isNotEmpty(reimbursePerson)) {
			dto.setReimbursePerson(reimbursePerson);
		}
		if (StringUtils.isNotEmpty(reimburseDept)) {
			dto.setReimburseDept(reimburseDept);
		}
		if (StringUtils.isNotEmpty(reimburseTime)) {
			dto.setReimburseTime(reimburseTime);
		}
		if (StringUtils.isNotEmpty(reimburseNo)) {
			dto.setReimburseNo(reimburseNo);
		}
		// 设置报销状态标志为"2"
		dto.setReimStatusFlag("2");
		dto.setUpdateTime(DateUtil.getCurrentTime(null));
		paymentTermsWriteMapper.updateById(dto);
	}

	@Override
	public void updatePaymentTerms(CmsPaymentTermsDto dto) {
		// 根据合同ID查询合同主表数据
		CmsContractDto contractDto = new CmsContractDto();
		contractDto.setId(dto.getContractId());
		List<CmsContractVo> contractVos = contractReadMapper.queryList(contractDto);

		if (contractVos != null && !contractVos.isEmpty()) {
			CmsContractVo contractVo = contractVos.get(0);
			CmsContractDto updateContractDto = new CmsContractDto();
			updateContractDto.setId(dto.getContractId());
			boolean needUpdate = false;

			// 比较付款方式
			if (!StringUtils.equals(contractVo.getPaymentType(), dto.getPaymentType())) {
				updateContractDto.setPaymentType(dto.getPaymentType());
				needUpdate = true;
			}

			// 比较合同总额
			if (contractVo.getTotalAmt() == null ||
					!contractVo.getTotalAmt().equals(dto.getTotalAmt())) {
				updateContractDto.setTotalAmt(dto.getTotalAmt());
				needUpdate = true;
			}

			// 如果有字段需要更新，则更新合同主表
			if (needUpdate) {
				contractReadMapper.updateById(updateContractDto);
			}
		}

		// 处理付款计划表
		if (dto.getPaymentList() != null && !dto.getPaymentList().isEmpty()) {
			// 先查询所有现有的付款计划
			List<CmsPaymentTermsDto> dtoList = payTermsReadMapper.selectList(
					new QueryWrapper<CmsPaymentTermsDto>()
							.eq("contract_id", dto.getContractId()));
			List<CmsPaymentTermsVo> existingPayments = dtoList.stream()
					.map(paymentTerms -> {
						CmsPaymentTermsVo vo = new CmsPaymentTermsVo();
						org.springframework.beans.BeanUtils.copyProperties(paymentTerms, vo);
						vo.setHospitalId(dto.getHospitalId());
						return vo;
					})
					.collect(Collectors.toList());

			// 遍历新的付款计划列表
			for (CmsPaymentTermsDto paymentTerm : dto.getPaymentList()) {
				if (paymentTerm.getId() != null) {
					// 对于已存在的记录，检查reimStatusFlag
					CmsPaymentTermsVo existingPayment = existingPayments.stream()
							.filter(p -> p.getId().equals(paymentTerm.getId()))
							.findFirst()
							.orElse(null);

					if (existingPayment != null &&
							StringUtils.isEmpty(existingPayment.getReimStatusFlag())) {
						// 只有当reimStatusFlag为空时才更新
						paymentTerm.setUpdateTime(DateUtil.getCurrentTime(null));
						// 设置当期付款金额
						paymentTerm.setCurrentPayAmt(paymentTerm.getTotalAmt());
						// 设置付款方式
						paymentTerm.setPaymentType(dto.getPaymentType());
						// 设置付款条件
						paymentTerm.setPaymentTerms(dto.getPaymentTerms());
						// 设置医院ID
						paymentTerm.setHospitalId(dto.getHospitalId());
						// 设置业务状态
						paymentTerm.setBusinessStatus(MedConst.TYPE_0);
						paymentTermsWriteMapper.updateById(paymentTerm);
					}
				} else {
					// 新增记录
					paymentTerm.setContractId(dto.getContractId());
					paymentTerm.setCreateTime(DateUtil.getCurrentTime(null));
					paymentTerm.setUpdateTime(DateUtil.getCurrentTime(null));
					// 设置当期付款金额
					paymentTerm.setCurrentPayAmt(paymentTerm.getTotalAmt());
					// 设置付款方式
					paymentTerm.setPaymentType(dto.getPaymentType());
					// 设置付款条件
					paymentTerm.setPaymentTerms(paymentTerm.getPaymentTerms());
					// 设置医院ID
					paymentTerm.setHospitalId(dto.getHospitalId());
					// 设置业务状态
					paymentTerm.setBusinessStatus(MedConst.TYPE_0);
					paymentTermsWriteMapper.insert(paymentTerm);
				}
			}

			// 删除不在新列表中的旧记录（前提是reimStatusFlag为空）
			for (CmsPaymentTermsVo existingPayment : existingPayments) {
				boolean exists = dto.getPaymentList().stream()
						.anyMatch(p -> p.getId() != null && p.getId().equals(existingPayment.getId()));

				if (!exists && StringUtils.isEmpty(existingPayment.getReimStatusFlag())) {
					paymentTermsWriteMapper.deleteById(existingPayment.getId());
				}
			}
		}
	}
}
