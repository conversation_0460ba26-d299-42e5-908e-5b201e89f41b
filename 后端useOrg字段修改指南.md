# 🔧 后端 useOrg 字段修改指南

## 📋 需要修改的文件清单

### 1. 实体类 - EcsReimContractTask.java
**文件路径**: `med-common/src/main/java/com/jp/med/common/dto/ecs/EcsReimContractTask.java`

在第97行 `@TableField("bankcode")` 之后添加：

```java
@TableField("bankcode")
private String bankcode;

/** 🆕 使用科室 */
@TableField("use_org")
private String useOrg;
```

### 2. VO类 - EcsReimContractTaskVo.java
**文件路径**: `med-ecs/src/main/java/com/jp/med/ecs/modules/reimMgt/vo/EcsReimContractTaskVo.java`

在第62行 `private String appyerName;` 之后添加：

```java
/** 申请人姓名 **/
private String appyerName;

/** 🆕 使用科室 */
private String useOrg;
```

### 3. 查询SQL - EcsReimContractTaskReadMapper.xml
**文件路径**: `med-ecs/src/main/resources/mapper/reimMgt/read/EcsReimContractTaskReadMapper.xml`

在第30行的SELECT语句中添加 `use_org` 字段：

**修改前**:
```xml
,a.ct_name as ctName ,a.ct_unified_code as ctUnifiedCode ,a.need_reim_amt as needReimAmt
```

**修改后**:
```xml
,a.ct_name as ctName ,a.ct_unified_code as ctUnifiedCode ,a.need_reim_amt as needReimAmt
,a.use_org as useOrg
```

### 4. 后端服务 - CmsPaymentTermsWriteServiceImpl.java
**文件路径**: `med-cms/src/main/java/com/jp/med/cms/modules/contractManage/service/write/impl/CmsPaymentTermsWriteServiceImpl.java`

在 `goGenerateReim` 方法中添加 `useOrg` 字段的设置。

**查找位置**: 在第177行 `taskDetail.setOrgId(appyerDept);` 之后添加

```java
// 报销科室
taskDetail.setOrgId(appyerDept);

// 🆕 设置使用科室
if (StringUtils.isNotEmpty(contractVo.getUseOrg())) {
    task.setUseOrg(contractVo.getUseOrg());
}
```

### 5. 接口参数处理 - CmsPaymentTermsController.java
**文件路径**: `med-cms/src/main/java/com/jp/med/cms/modules/contractManage/controller/CmsPaymentTermsController.java`

在 `goGenerateReim` 接口方法中添加参数处理：

```java
@PostMapping("/goGenerateReim")
public CommonResult<?> goGenerateReim(@RequestBody Map<String, Object> params) {
    try {
        // 获取付款条款ID
        Integer paymentId = Integer.valueOf(params.get("id").toString());
        
        // 🆕 获取使用科室信息
        String useOrg = params.get("useOrg") != null ? params.get("useOrg").toString() : null;
        String contractId = params.get("contractId") != null ? params.get("contractId").toString() : null;
        
        // 构建DTO
        CmsPaymentTermsDto dto = new CmsPaymentTermsDto();
        dto.setId(paymentId);
        
        // 🆕 如果有使用科室信息，设置到DTO中
        if (StringUtils.isNotEmpty(useOrg)) {
            // 这里可能需要查询合同信息并设置useOrg
            // 具体实现根据业务逻辑调整
        }
        
        cmsPaymentTermsWriteService.goGenerateReim(dto);
        return CommonResult.success();
    } catch (Exception e) {
        log.error("生成报销任务失败", e);
        return CommonResult.error("生成报销任务失败: " + e.getMessage());
    }
}
```

## 🔄 修改步骤

### 步骤1: 修改实体类
1. 打开 `EcsReimContractTask.java`
2. 在 `bankcode` 字段后添加 `useOrg` 字段
3. 确保添加了正确的 `@TableField` 注解

### 步骤2: 修改VO类
1. 打开 `EcsReimContractTaskVo.java`
2. 在 `appyerName` 字段后添加 `useOrg` 字段

### 步骤3: 修改查询SQL
1. 打开 `EcsReimContractTaskReadMapper.xml`
2. 在SELECT语句中添加 `,a.use_org as useOrg`

### 步骤4: 修改业务服务
1. 打开 `CmsPaymentTermsWriteServiceImpl.java`
2. 在 `goGenerateReim` 方法中添加 `useOrg` 设置逻辑

### 步骤5: 修改控制器
1. 打开 `CmsPaymentTermsController.java`
2. 在 `goGenerateReim` 接口中添加参数处理

## ✅ 验证步骤

1. **编译检查**: 确保所有修改的文件能正常编译
2. **数据库验证**: 确认 `ecs_reim_contract_task` 表有 `use_org` 字段
3. **接口测试**: 测试推送报销任务接口，确认 `useOrg` 能正确保存
4. **查询验证**: 测试查询接口，确认能返回 `useOrg` 信息

## 🎯 预期效果

修改完成后：
1. 前端推送报销任务时传递的 `useOrg` 信息能正确保存到数据库
2. 查询报销任务时能返回 `useOrg` 信息
3. 报销系统能获取到合同的使用科室信息

## 📝 注意事项

1. 确保数据库字段已经添加
2. 注意字段名的一致性（数据库用下划线，Java用驼峰）
3. 添加适当的空值检查
4. 考虑向后兼容性
