package com.jp.med.common.dto.ecs;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 合同报销任务
 * <AUTHOR>
 * @email -
 * @date 2024-07-24 17:13:38
 */
@Data
@TableName("ecs_reim_contract_task" )
public class EcsReimContractTask extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 申请人编号 */
    @TableField("appyer")
    private String appyer;

    /** 申请科室 */
    @TableField("appyer_dept")
    private String appyerDept;

    /** 合同编码 */
    @TableField("ct_code")
    private String ctCode;

    /** 合同分类编码 */
    @TableField("type_code")
    private String typeCode;

    /** 合同附件 */
    @TableField("contract_att")
    private String contractAtt;

    /** 合同附件名 */
    @TableField("contract_att_name")
    private String contractAttName;

    /** 合同总额 */
    @TableField("total_amt")
    private BigDecimal totalAmt;

    /** 当前阶段 */
    @TableField("stage")
    private String stage;

    /** 合同id */
    @TableField("contract_id")
    private Integer contractId;

    /** 所占比例 */
    @TableField("proportion")
    private String proportion;

    /** 计划付款时间 */
    @TableField("payment_time")
    private String paymentTime;

    /** 报销标志 0:未报销 1:已报销 */
    @TableField("reim_flag")
    private String reimFlag;

    /** 付款期数id **/
    @TableField("payment_id")
    private Integer paymentId;

    /** 相对方名称/收款乙方名称 **/
    @TableField("opposite_name")
    private String oppositeName;

    /** 报销id **/
    @TableField("reim_id")
    private Integer reimId;

    /** 合同明细 **/
    @TableField(exist = false)
    private List<EcsReimContractTaskDetail> details;

    @TableField("bank")
    private String bank;

    @TableField("acctname")
    private String acctname;

    @TableField("bankcode")
    private String bankcode;

    /** 🆕 使用科室 */
    @TableField("use_org")
    private String useOrg;

    /**
     * 合同名称
     */
    @TableField("ct_name")
    private String ctName;

    /**
     * 合同统一编码
     */
    @TableField("ct_unified_code")
    private String ctUnifiedCode;

    /**
     * 待报销金额
     */
    @TableField("need_reim_amt")
    private BigDecimal needReimAmt;

    /**
     * 付款方式
     */
    @TableField("payment_type")
    private String paymentType;
}
