<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ams.modules.property.mapper.read.AmsPropertyReadMapper">
    <sql id="selectColumn">
        a.id                            key,
        a.id                         as id,
        a.funding_nature             as fundingNature,
        a.asset_code                 as
                                        assetCode,
        a.asset_name                 as assetName,
        a.serial_number              as serialNumber,
        a.asset_type                 as
                                        assetType,
        a.asset_category             as assetCategory,
        a.asset_status               as assetStatus,
        a.financial_assistance_funds as financialAssistanceFunds,
        a.own_funds                  as ownFunds,
        a.asset_mol                  as assetMol,
        a.asset_nav                  as assetNav,
        a.nbv                        as nbv,
        a.rv                         as rv,
        a.genname                    as
                                        genname,
        a.spec                       as spec,
        a.incr_way                   as incrWay,
        a.source                     as source,
        a.other_ref                  as otherRef,
        a.supplier                   as supplier,
        a.supplier_coner             as supplierConer,
        a.supplier_tel               as supplierTel,
        a.fse                        as fse,
        a.cs                         as cs,
        a.brand                      as brand,
        a.manufacturer               as manufacturer,
        a.unit                       as unit,
        a.describe                   as describe,
        a.dept_use                   as deptUse,
        a.dept                       as dept,
        a.storage_area               as
                                        storageArea,
        as_cfg.storage_area_name     as storageAreaName,
        a.location                   as location,
        a.exp                        as
                                        exp,
        a.used_exp                   as usedExp,
        a.unit_price                 as unitPrice,
        a.dm                         as dm,
        a.ul                         as ul,
        a.dep                        as dep,
        a.am_status                  as amStatus,
        a.oow_date                   as oowDate,
        a.rcn                        as rcn,
        a.sn                         as sn,
        a.wp                         as wp,
        a.cntr_code                  as cntrCode,
        a.cntr_name                  as cntrName,
        a.invono                     as invono,
        a.oemw                       as oemw,
        a.purc_date                  as purcDate,
        a.inst_date                  as instDate,
        a.opening_date               as openingDate,
        a.stoin_date                 as stoinDate,
        a.acp_date                   as acpDate,
        a.stoout_date                as stooutDate,
        a.asset_used
                                     as assetUsed,
        a.oop                        as oop,
        a.gf                         as gf,
        a.research_funding           as researchFunding,
        a.tef                        as tef,
        a.adjact                     as adjact,
        a.imp                        as imp,
        a.uid                        as uid,
        a.tpn                        as tpn,
        a.wts                        as wts,
        a.ipd                        as ipd,
        a.epd                        as epd,
        a.pd                         as pd,
        a.resper                     as resper,
        a.us                         as us,
        a.fc                         as fc,
        a.mdr                        as mdr,
        a.rsl                        as
                                        rsl,
        a.cm                         as cm,
        a.ls                         as ls,
        a.ed                         as ed,
        a.tcm                        as tcm,
        a.te                         as te,
        a.se                         as se,
        a.rs                         as rs,
        a.ac                         as ac,
        a.ae                         as ae,
        a.manu_date                  as manuDate,
        a.me                         as me,
        a.mc                         as mc,
        a.cnt                        as cnt,
        a.resr                       as resr,
        a.area                       as area,
        a.issucert_date              as issucertDate,
        a.proof_of_title             as
                                        proofOfTitle,
        a.certificate_num            as certificateNum,
        a.tenure                     as tenure,
        a.loc                        as loc,
        a.land_use_rights_type       as landUseRightsType,
        a.property_form              as propertyForm,
        a.hospital_id
                                     as hospitalId,
        a.deprrat_mon                as deprratMon,
        a.depr_mon                   as deprMon,
        a.used_mon                   as usedMon,
        a.lpn                        as lpn,
        a.redc_way                   as redcWay,
        a.inpter                     as inpter,
        a.inpt_date                  as inptDate,
        a.is_prepare_vouchers        as isPrepareVouchers,
        a.is_chk                     as isChk,
        a.chker                      as chker,
        a.memo                       as
                                        memo,
        a.land_area                  as landArea,
        a.co_owner                   as coOwner,
        a.zkc                        as zkc,
        a.entry_form                 as
                                        entryForm,
        a.land_souc                  as landSouc,
        a.usage                      as usage,
        a.property_char              as propertyChar,
        a.co_date                    as coDate,
        a.property_owner             as propertyOwner,
        a.building_structure         as
                                        buildingStructure,
        a.use                        as use,
        a.value_type                 as valueType,
        a.engine_no                  as engineNo,
        a.asset_classification       as assetClassification,
        a.land_usage_area            as landUsageArea,
        a.land_user                  as landUser,
        a.land_used                  as landUsed,
        a.owner_of_house             as ownerOfHouse,
        a.avn                        as
                                        avn,
        a.dev_used                   as devUsed,
        a.vin                        as vin,
        a.is_split                   as isSplit,
        a.is_canc                    as isCanc,
        a.canc_psn                   as cancPsn,
        a.canc_date                  as cancDate,
        a.cert_no                    as certNo,
        a.depr_m                     as deprM,
        a.storage_location           as storageLocation,
        a.obtain_way                 as obtainWay,
        a.obtain_date                as
                                        obtainDate,
        a.entry_status               as entryStatus,
        a.entry_date                 as entryDate,
        a.pof                        as pof,
        a.jvn                        as
                                        jvn,
        a.completed_date             as completedDate,
        a.dsgn_used                  as dsgnUsed,
        a.holder                     as holder,
        a.depr_status                as deprStatus,
        a.heating_area               as heatingArea,
        a.danger_area                as dangerArea,
        a.un_gf                      as unGf,
        a.idle_area                  as idleArea,
        a.self_area                  as selfArea,
        a.lend_area                  as lendArea,
        a.hire_area                  as hireArea,
        a.other_area                 as otherArea,
        a.regis_date                 as regisDate,
        a.vehicle_origin             as vehicleOrigin,
        a.vehicle_brand              as vehicleBrand,
        a.displacement               as
                                        displacement,
        a.compilation_status         as compilationStatus,
        a.vehicle_usage              as vehicleUsage,
        a.press                      as press,
        a.publication_date           as publicationDate,
        a.file_no                    as fileNo,
        a.storage_period             as storagePeriod,
        a.cocr                       as cocr,
        a.origin                     as origin,
        a.collection_age             as
                                        collectionAge,
        a.planting_age               as plantingAge,
        a.planting_year              as plantingYear,
        a.genus                      as
                                        genus,
        a.producer                   as producer,
        a.shared_area                as sharedArea,
        a.exclusive_area             as
                                        exclusiveArea,
        a.land_level                 as landLevel,
        a.asset_type_n               as assetTypeN,
        a.type                       as type,
        a.sa_code                    as saCode,
        c.ccm_name                   as ccmName,
        b.asset_type_name            as assetTypeName,
        b.stock_cfg
                                     as stockCfg,
        f.asset_type_name            as assetTypeNName,
        e.org_name                   as deptName,
        coalesce(d.org_name,
                 (SELECT string_agg(t2.org_name, ',')
                  FROM ams_depr_asgn t1
                           INNER JOIN hrm_org t2 ON t2.org_id = t1.org_id
                  WHERE t1.fa_code = a.fa_code),
                 ','
        )                            as deptUseName
                ,
        a.pre_warehousing            as preWwrehousing,
        a.is_info_dept_warehouse     as isInfoDeptWarehouse
    </sql>


    <select id="queryList0" resultType="com.jp.med.ams.modules.property.vo.AmsPropertyVo">
        select
        <choose>
            <when test="type != null and type == 2">
                replace(a.fa_code, 'W', nfm.remark) as faCode,
            </when>
            <otherwise>
                a.fa_code as faCode,
            </otherwise>
        </choose>

        <if test="onlyQueryId">
            a.id,
            a.hospital_id as hospitalId
        </if>
        <if test="!onlyQueryId">
            <include refid="selectColumn"/>
        </if>
        from
        <if test="queryHistoryDataMonth != null and queryHistoryDataMonth != ''">
            ams_property_monthly_snapshot
        </if>
        <if test="queryHistoryDataMonth == null">
            ams_property
        </if>
        as a
        <!-- 非固定资产前缀添加 -->
        <if
                test="type != null and type.equals('2'.toString())">





            left join ( WITH RECURSIVE
            type_hierarchy
            AS ( <!--        &#45;&#45; 基础情况：包含所有资产类型及其父级--> SELECT asset_type_code, parent_code
            FROM ams_type_cfg
            WHERE asset_type_code IS NOT NULL
            UNION ALL <!--        &#45;&#45; 递归部分：获取所有上层父级-->
            SELECT th.asset_type_code, atc.parent_code
            FROM ams_type_cfg atc
            JOIN type_hierarchy th ON atc.asset_type_code = th.parent_code ),
            type_ancestors
            AS ( <!--        &#45;&#45; 添加自身：将每个资产类型作为自己的祖先--> SELECT asset_type_code AS
            asset_type_code,
            asset_type_code AS
            ancestor_code
            FROM ams_type_cfg
            WHERE asset_type_code IS NOT NULL
            UNION <!--        &#45;&#45; 保留原有的祖先查询-->
            SELECT DISTINCT th.asset_type_code,
            th.parent_code AS ancestor_code
            FROM type_hierarchy th ),
            dept_types AS
            (SELECT anaad.dept, ta.asset_type_code, anaad.remark
            FROM ams_nf_asset_admin_dept anaad
            JOIN
            type_ancestors ta ON ta.ancestor_code = ANY (string_to_array(anaad.asset_types, ','))
            WHERE anaad.active_flag = '1')
            SELECT dt.asset_type_code AS type,
            string_agg(dt.dept, ',') AS
            depts,
            dt.remark
            FROM dept_types dt
            GROUP BY dt.asset_type_code, dt.remark, dt.remark ) nfm
        on nfm.type = a.asset_type
        </if>
        left join
        <choose>
            <when test="assetType != null and assetType != ''">
                (SELECT h.*
                    FROM (WITH RECURSIVE temp AS (SELECT *
                    FROM ams_type_cfg r
                    where asset_type_code = #{assetType,jdbcType=VARCHAR}
                    UNION ALL
                    SELECT b.*
                    FROM ams_type_cfg b,
                    temp t
                    WHERE b.parent_code = t.asset_type_code)
                    SELECT *
                    FROM temp) h) b
            </when>
            <otherwise>
                ams_type_cfg b
            </otherwise>
        </choose>
        on a.asset_type =b.asset_type_code
            LEFT JOIN ams_cfg_ccmd c on a.asset_category = c.ccm_code and
            c.active_flag = '1'
            LEFT JOIN hrm_org d ON a.dept_use = d.org_id LEFT JOIN hrm_org e ON
            a.dept = e.org_id

        <!--        处理多使用科室问题-->
        LEFT JOIN (select storage_area_code as storage_area_code_cfg,storage_area
            as storage_area_name from ams_storage_cfg) as_cfg ON split_part(a.storage_area, ' ', 1) =
            as_cfg.storage_area_code_cfg
            LEFT JOIN
        <choose>
            <when test="assetTypeN != null and assetTypeN != ''">
                (SELECT h.* FROM ( WITH RECURSIVE
                    temp AS (SELECT * FROM ams_typen_cfg r where asset_type_code =
                    #{assetTypeN,jdbcType=VARCHAR} UNION ALL SELECT b.* FROM ams_typen_cfg b, temp t WHERE
                    b.parent_code = t.asset_type_code ) SELECT * FROM temp ) h ) f
            </when>
            <otherwise>
                ams_typen_cfg f
            </otherwise>
        </choose>
        on a.asset_type_n =
            f.asset_type_code
        <where>
            <if test="queryHistoryDataMonth != null and queryHistoryDataMonth != ''">
                a.monthly_snapshot = #{queryHistoryDataMonth}
            </if>
            <if
                    test="faCodes != null and faCodes.size() > 0">
                and a.fa_code in
                <foreach
                        collection="faCodes" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
            <if
                    test="faCode != null and faCode != ''">
                and a.fa_code = #{faCode,jdbcType=INTEGER}
            </if>
            <if
                    test="excludeFaCodes != null and excludeFaCodes.size() > 0">
                and a.fa_code not in
                <foreach
                        collection="excludeFaCodes" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>


            <if test="asset != '' and asset != null">
                and (a.fa_code like concat('%'
                  , concat(#{asset,jdbcType=VARCHAR}
                  , '%'))
                 or a.asset_name
                    like concat('%'
                  , concat(#{asset,jdbcType=VARCHAR}
                  , '%')))
            </if>
            <if
                    test="assetStatus != '' and assetStatus != null">
                and a.asset_status = #{assetStatus,jdbcType=VARCHAR}
            </if>
            <if test="dept != null and dept != ''">
                and dept = #{dept,jdbcType=VARCHAR}
            </if>

            <!--多使用科室-->
            <if test="deptUse != null and deptUse != ''">
                and #{deptUse,jdbcType=VARCHAR} = ANY (string_to_array(a.dept_use
                  , ','))
            </if>
            <if test="isChk != null and isChk != ''">
                and a.is_chk = #{isChk,jdbcType=VARCHAR}
            </if>
            <if test="isCanc != null and isCanc != ''">
                and a.is_canc = #{isCanc,jdbcType=VARCHAR}
            </if>
            <if test="assetType != null and assetType != ''">
                and
                    b.asset_type_name is not null
            </if>
            <if test="type != null and type != ''">
                and a.type = #{type,jdbcType=VARCHAR}
            </if>
            <if test="assetTypeN != null and assetTypeN != ''">
                and
                    f.asset_type_name is not null
            </if>


            <if test="id != null">
                and a.id = #{id,jdbcType=INTEGER}
            </if>

            <if
                    test="canSplit">
                and a.cnt
               &gt; 1
            </if>

            <choose>
                <when test="behalfApprovalDepts != null and behalfApprovalDepts.size() != 0">
                    and (
                        string_to_array(a.dept_use
                      , ','):: varchar [] &amp;&amp;
                        ARRAY [
                    <foreach collection="behalfApprovalDepts" item="item" separator="," open="" close="">
                        #{item}
                    </foreach>
                    ]

                    <!--    /* 部门条件 */-->

                    <!--    /* 资产类型条件 */-->
                    <if
                            test="(proxyAssetType != null and proxyAssetType.size() > 0) or (proxyAssetTypeN != null and proxyAssetTypeN.size() > 0)">
                        or (
                        <if test="proxyAssetType != null and proxyAssetType.size() > 0">
                            a.asset_type IN ( WITH
                                RECURSIVE asset_types AS ( <!--                        /* 基础查询 - 直接匹配传入的资产类型 */-->
                            SELECT asset_type_code FROM ams_type_cfg WHERE asset_type_code
                                IN
                            <foreach collection="proxyAssetType" item="item" separator="," open="(" close=")">
                                #{item}
                            </foreach>
                            UNION ALL <!--                        /* 递归查询 - 查找所有子级 */--> SELECT
                            b.asset_type_code FROM ams_type_cfg b INNER JOIN
                            asset_types t ON b.parent_code = t.asset_type_code ) SELECT asset_type_code FROM asset_types
                            )
                        </if>

                        <if test="proxyAssetTypeN != null and proxyAssetTypeN.size() > 0">
                            <if test="proxyAssetType != null and proxyAssetType.size() > 0">
                                OR
                            </if>
                            a.asset_type_n IN ( WITH RECURSIVE asset_types_n AS
                                              ( <!--                        /* 基础查询 - 直接匹配传入的资产类型N */--> SELECT
                            asset_type_code FROM
                            ams_typen_cfg WHERE asset_type_code IN
                            <foreach collection="proxyAssetTypeN" item="item"
                                     separator="," open="(" close=")">
                                #{item}
                            </foreach>
                            UNION ALL <!--                        /* 递归查询 - 查找所有子级 */-->
                            SELECT b.asset_type_code FROM ams_typen_cfg b INNER JOIN asset_types_n t ON b.parent_code =
                                t.asset_type_code ) SELECT asset_type_code FROM asset_types_n )
                        </if>
                        )
                    </if>
                    )
                </when>

                <when test="curSysOrgId != null and curSysOrgId != ''">
                    and ( <!--                        /* 当前系统组织ID条件 */-->


                    #{curSysOrgId,jdbcType=VARCHAR}= ANY (string_to_array(a.dept_use
                                                         , ','))


                    <!--      /* 资产类型条件 */-->
                    <if
                            test="(proxyAssetType != null and proxyAssetType.size() > 0) or (proxyAssetTypeN != null and proxyAssetTypeN.size() > 0)">
                        or (
                        <if test="proxyAssetType != null and proxyAssetType.size() > 0">
                            a.asset_type IN ( WITH
                                RECURSIVE asset_types AS ( <!--                        /* 基础查询 - 直接匹配传入的资产类型 */-->
                            SELECT asset_type_code FROM ams_type_cfg WHERE asset_type_code
                                IN
                            <foreach collection="proxyAssetType" item="item" separator="," open="(" close=")">
                                #{item}
                            </foreach>
                            UNION ALL <!--                        /* 递归查询 - 查找所有子级 */--> SELECT
                            b.asset_type_code FROM ams_type_cfg b INNER JOIN
                            asset_types t ON b.parent_code = t.asset_type_code ) SELECT asset_type_code FROM asset_types
                            )
                        </if>

                        <if test="proxyAssetTypeN != null and proxyAssetTypeN.size() > 0">
                            <if test="proxyAssetType != null and proxyAssetType.size() > 0">
                                OR
                            </if>
                            a.asset_type_n IN ( WITH RECURSIVE asset_types_n AS
                                              ( <!--                        /* 基础查询 - 直接匹配传入的资产类型N */--> SELECT
                            asset_type_code FROM
                            ams_typen_cfg WHERE asset_type_code IN
                            <foreach collection="proxyAssetTypeN" item="item"
                                     separator="," open="(" close=")">
                                #{item}
                            </foreach>
                            UNION ALL <!--                        /* 递归查询 - 查找所有子级 */-->
                            SELECT b.asset_type_code FROM ams_typen_cfg b INNER JOIN asset_types_n t ON b.parent_code =
                                t.asset_type_code ) SELECT asset_type_code FROM asset_types_n )
                        </if>
                        )
                    </if>
                </when>
            </choose>

            <if
                    test="inptDateRange != null and inptDateRange.length > 0">
                <![CDATA[
                and (a.inpt_date >= #{inptDateRange.[0],jdbcType=VARCHAR}
                and a.inpt_date <= #{inptDateRange.[1],jdbcType=VARCHAR})
                ]]>
            </if>
            <if
                    test="openingDateRange != null and openingDateRange.length > 1">
                <![CDATA[
                and (a.opening_date >= #{openingDateRange.[0],jdbcType=VARCHAR}
                and a.opening_date <= #{openingDateRange.[1],jdbcType=VARCHAR})
                ]]>
            </if>


            <if
                    test="ids != null and ids.size() > 0">
                and a.id in
                <foreach collection="ids"
                         item="item" separator="," open="(" close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
            <if
                    test="(deptField == 'dept') and deptCode != null and deptCode != ''">
                and dept in ( with recursive dept_codes as ( select org_id
                  , org_name
                  , org_parent_id from hrm_org where org_id = #{deptCode} union all select hoc.org_id
                  , hoc.org_name
                  , hoc.org_parent_id from hrm_org hoc join dept_codes dc on hoc.org_parent_id =
                    dc.org_id ) select org_id from dept_codes )
            </if>

            <if test="(deptField == 'dept_use') and deptCode != null and deptCode != ''">
                and string_to_array(dept_use
                  , ',')::varchar [] &amp;&amp;
                    ARRAY (
                    with recursive dept_codes as (
                    select org_id
                  , org_name
                  , org_parent_id
                    from hrm_org
                    where org_id = #{deptCode}
                    union all
                    select hoc.org_id
                  , hoc.org_name
                  , hoc.org_parent_id
                    from hrm_org hoc
                    join dept_codes dc on hoc.org_parent_id = dc.org_id
                    )
                    select org_id from dept_codes
                    )
            </if>

            <if
                    test="deptField == 'storage_area' and deptCode != null and deptCode != ''">
                and ${deptField} in ( with recursive storage_codes as ( select a.storage_area
                  , a.storage_area_code
                  , a.parent_id from ams_storage_cfg a where a.storage_area_code = #{deptCode} union all select
                    hoc.storage_area
                  , hoc.storage_area_code
                  , hoc.parent_id from
                    ams_storage_cfg hoc join storage_codes dc on hoc.parent_id = dc.storage_area_code ) select
                    storage_area_code from storage_codes )
            </if>

            <choose>
               <when test="isCanc != null and isCanc == -1">
                    and a.is_canc in ('0','1')
                </when>
                <when test="isCanc == null or isCanc == '' or isCanc == 0">
                    and a.is_canc = '0'
                </when>
                <when test="isCanc != null and isCanc == 1">
                    and a.is_canc = '1'
                </when>
            </choose>

            <!-- 信息科库房资产查询条件 -->
            <if test="isInfoDeptWarehouse != null and isInfoDeptWarehouse != ''">
                and a.is_info_dept_warehouse = #{isInfoDeptWarehouse,jdbcType=VARCHAR}
            </if>

            <!--            默认不显示已注销-->
            <if
                    test="preWarehousing != '' and preWarehousing != null">
                and a.pre_warehousing = #{preWarehousing,jdbcType=VARCHAR}
            </if>


            <if test="extendForm != null">
                <foreach collection="extendForm" item="item">
                    <choose>
                        <when test='item.key == "none_pictures" or item.key == "nonePictures"'>
                            AND a.fa_code in (select fa_code from
                                ams_property where fa_code not in (select fa_code from ams_records where fa_code is not null
                                group by fa_code) group by fa_code)
                        </when>
                        <otherwise>
                            <if test='item.dataType == "1"'>
                                <choose>
                                    <when test='item.searchType == "3"'>
                                        and (${item.key} = ''
                                         or ${item.key} is null)
                                    </when>
                                    <otherwise>
                                        <!-- 根据操作符生成对应的SQL条件 -->
                                        <choose>
                                            <when test='item.operator == "gte"'>
                                                <!-- 大于等于 -->
                                                <if test="item.listData[0] != '' and item.listData[0] != null">
                                                    and ${item.key} >= #{item.listData[0]}
                                                </if>
                                                <if test="item.listData[1] != '' and item.listData[1] != null">
                                                    and ${item.key} >= #{item.listData[1]}
                                                </if>
                                            </when>
                                            <when test='item.operator == "lte"'>
                                                <!-- 小于等于 -->
                                                <if test="item.listData[0] != '' and item.listData[0] != null">
                                                    <![CDATA[
                                                    and ${item.key} <= #{item.listData[0]}
                                                    ]]>
                                                </if>
                                                <if test="item.listData[1] != '' and item.listData[1] != null">
                                                    <![CDATA[
                                                    and ${item.key} <= #{item.listData[1]}
                                                    ]]>
                                                </if>
                                            </when>
                                            <when test='item.operator == "gt"'>
                                                <!-- 大于 -->
                                                <if test="item.listData[0] != '' and item.listData[0] != null">
                                                    <![CDATA[
                                                    and ${item.key} > #{item.listData[0]}
                                                    ]]>
                                                </if>
                                                <if test="item.listData[1] != '' and item.listData[1] != null">
                                                    <![CDATA[
                                                    and ${item.key} > #{item.listData[1]}
                                                    ]]>
                                                </if>
                                            </when>
                                            <when test='item.operator == "lt"'>
                                                <!-- 小于 -->
                                                <if test="item.listData[0] != '' and item.listData[0] != null">
                                                    <![CDATA[
                                                    and ${item.key} < #{item.listData[0]}
                                                    ]]>
                                                </if>
                                                <if test="item.listData[1] != '' and item.listData[1] != null">
                                                    <![CDATA[
                                                    and ${item.key} < #{item.listData[1]}
                                                    ]]>
                                                </if>
                                            </when>
                                            <when test='item.operator == "eq"'>
                                                <!-- 等于 -->
                                                <if test="item.listData[0] != '' and item.listData[0] != null">
                                                    and ${item.key} = #{item.listData[0]}
                                                </if>
                                                <if test="item.listData[1] != '' and item.listData[1] != null">
                                                    and ${item.key} = #{item.listData[1]}
                                                </if>
                                            </when>
                                            <when test='item.operator == "between"'>
                                                <!-- 范围查询（主要用于日期） -->
                                                <if test="item.listData[0] != '' and item.listData[0] != null">
                                                    and ${item.key} >= #{item.listData[0]}
                                                </if>
                                                <if test="item.listData[1] != '' and item.listData[1] != null">
                                                    <![CDATA[
                                                    and ${item.key} <= #{item.listData[1]}
                                                    ]]>
                                                </if>
                                            </when>
                                            <otherwise>
                                                <!-- 向下兼容：没有操作符时使用原有逻辑（范围查询） -->
                                                <if test="item.listData[0] != '' and item.listData[0] != null">
                                                    and ${item.key} >= #{item.listData[0]}
                                                </if>
                                                <if test="item.listData[1] != '' and item.listData[1] != null">
                                                    <![CDATA[
                                                    and ${item.key} <= #{item.listData[1]}
                                                    ]]>
                                                </if>
                                            </otherwise>
                                        </choose>
                                    </otherwise>
                                </choose>
                            </if>
                            <if
                                    test='item.dataType == "2"'>
                                <choose>
                                    <when test='item.searchType == "3"'>
                                        and (${item.key} = ''
                                         or ${item.key} is null)
                                    </when>
                                    <otherwise>
                                        and ${item.key} = #{item.numData}
                                    </otherwise>
                                </choose>
                            </if>
                            <if
                                    test='item.dataType == "3"'>
                                <if test='item.searchType == "1"'>
                                    and ${item.key} = #{item.stringData}
                                </if>
                                <if test='item.searchType == "2"'>
                                    and ${item.key} like concat('%'
                                      , concat(#{item.stringData}
                                      , '%'))
                                </if>
                                <if test='item.searchType == "3"'>
                                    and (${item.key} =
                                        ''
                                     or ${item.key} is null)
                                </if>
                            </if>
                        </otherwise>
                    </choose>
                </foreach>
            </if>
        </where>
        <!-- 动态排序支持 -->
        ORDER BY
        <choose>
            <!-- 检查是否有排序字段 -->
            <when test="extendForm != null">
                <foreach collection="extendForm" item="item" separator="">
                    <if test="item.isSortField != null and item.isSortField == true and item.sortOrder != null">
                        ${item.key} ${item.sortOrder},
                    </if>
                </foreach>
            </when>
        </choose>
        <!-- 默认排序 -->
        <choose>
            <when test='type != null and type == "1".toString()'>
                COALESCE (CAST (a.fa_code AS INT), a.id) ASC
            </when>
            <otherwise>
                a.id ASC
            </otherwise>
        </choose>
    </select>


    <!-- 查询资产汇总统计数据 -->
    <select id="queryListSum0" resultType="com.jp.med.ams.modules.property.vo.AmsPropertyVo">
        WITH
        xtem as ( select o.* from ( select a.id,
                                           a.fa_code as faCode, <!-- 资产原值 -->
        a.asset_nav as assetNav, <!-- 资产净值 -->
        a.nbv as nbv, <!-- 资产净值 -->
        a.dep as dep, <!-- 是否注销 -->
        a.is_canc as isCanc, <!-- 医院ID -->
        a.hospital_id as hospitalId
        from ams_property a
        <where>
            <if test="ids != null and ids.size() > 0">
                and a.id in
                <foreach collection="ids"
                         item="item" separator="," open="(" close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
        </where>
        ) o
        order by o.id )
        select
        <!--         &#45;&#45; 未报废资产统计-->
        SUM(CASE
                WHEN xtem.isCanc = '0' THEN assetNav
                ELSE 0
            END)                                             AS totalNRassetNav,
        SUM(CASE WHEN xtem.isCanc = '0' THEN dep ELSE 0 END) AS totalNRdep,
        SUM(CASE WHEN xtem.isCanc = '0' THEN nbv ELSE 0 END) AS totalNRnbv,
        COUNT(CASE
                  WHEN
                      xtem.isCanc = '0'
                      THEN 1 END)                            AS totalNRnum,
        <!--                             &#45;&#45; 已报废资产统计 -->
        SUM(CASE
                WHEN xtem.isCanc = '1' OR
                     xtem.isCanc IS NULL THEN assetNav
                ELSE 0
            END)                                              AS totalHRassetNav,
        SUM(CASE
                WHEN xtem.isCanc
                         = '1' OR xtem.isCanc IS NULL THEN dep
                ELSE 0 END)                                   AS totalHRdep,
        SUM(CASE
                WHEN xtem.isCanc =
                     '1' OR xtem.isCanc IS NULL THEN nbv
                ELSE 0 END)                                   AS totalHRnbv,
        COUNT(CASE
                  WHEN xtem.isCanc =
                       '1' OR xtem.isCanc IS NULL THEN 1 END) AS totalHRnum,
        <!--        &#45;&#45; 全部资产统计-->
        SUM(CASE
                WHEN xtem.isCanc IN
                     ('1', '0', NULL) THEN assetNav
                ELSE 0 END)       AS totalALassetNav,
        SUM(CASE
                WHEN xtem.isCanc IN
                     ('1', '0', NULL) THEN dep
                ELSE 0 END)       AS totalALdep,
        SUM(CASE
                WHEN xtem.isCanc IN ('1', '0',
                                     NULL) THEN nbv
                ELSE 0 END)       AS totalALnbv,
        COUNT(CASE
                  WHEN xtem.isCanc IN ('1', '0', NULL)
                      THEN 1 END) AS totalALnum,
        xtem.hospitalId
        from xtem
        group by xtem.hospitalId
    </select>


    <select id="queryRecords" resultType="com.jp.med.ams.modules.property.vo.AmsRecordsVo">
        select id           as id,
               id           as uuid,
               fa_code      as faCode,
               records_type as recordsType,
               attachment   as
                               attachment,
               upload_time  as uploadTime
        from ams_records
        <where>
            flag = '1'
            <if
                    test="faCode != null and faCode != ''">
                and fa_code = #{faCode,jdbcType=VARCHAR}
            </if>

            <if
                    test="id != null and id != ''">
                and property_id = #{id}
            </if>
            <if
                    test="scrapFacode != null and scrapFacode != ''">
                and scrap_facode = #{scrapFacode}
            </if>
            <if
                    test="scrapId != null">
                and scrap_id = #{scrapId}
            </if>
        </where>
    </select>

    <select id="querySon" resultType="com.jp.med.ams.modules.property.vo.AmsPropertyVo">
        select a.id
                                  as id,
               a.asset_type       as assetType,
               a.asset_mol        as assetMol,
               a.asset_name       as assetName,
               a.asset_code       as assetCode,
               a.dept_use         as deptUse,
               a.dept             as dept,
               a.storage_area     as
                                     storageArea,
               a.storage_location as storageLocation,
               a.serial_number    as serialNumber,
               a.tpn
                                  as tpn,
               a.uid              as uid,
               a.type             as type,
               a.fa_code          as faCode,
               a.asset_status     as assetStatus,
               c.asset_type_name  as assetTypeName,
        <!--               d.org_name                     as deptUseName,-->
        coalesce(d.org_name,
                 (SELECT string_agg(t2.org_name, ',')
                  FROM ams_depr_asgn t1
                           INNER JOIN hrm_org t2 ON t2.org_id = t1.org_id
                  WHERE t1.fa_code = a.fa_code),
                 ','
        )                              as deptUseName,
        e.org_name                     as deptName,
        case
            when f.storage_area is not null then f.storage_area
            else a.storage_area end    as
                                          storageAreaName,
        case
            when g.storage_area is not null then g.storage_area
            else
                a.storage_location end as storageLocationName,
        n.asset_type_name              as assetTypeNName
        from ams_property a
                 left join ams_type_cfg c on c.asset_type_code = a.asset_type
                 left join
             hrm_org d on d.org_id = a.dept_use
                 left join hrm_org e on e.org_id = a.dept
                 left join
             ams_storage_cfg f on f.storage_area_code = a.storage_area
                 left join ams_storage_cfg g on
            g.storage_area_code = a.storage_location
                 left join ams_typen_cfg n on a.asset_type_n =
                                              n.asset_type_code
        where a.sa_code = #{faCode,jdbcType=VARCHAR}
          and a.hospital_id =
              #{hospitalId}
        order by a.id
    </select>
    <!-- 查询最大资产编码 -->
    <select id="queryMaxAssetCode" resultType="java.lang.Long">
        select COALESCE(max(cast(replace(replace(replace(replace(asset_code, 'CF', ''), 'W', ''), 'S', ''), 'D',
                                         '') as bigint)), 0) as assetCode
        from ams_property
        where hospital_id = #{hospitalId}
          and sa_code is null
          and type = #{type,jdbcType=VARCHAR}
    </select>

    <!-- 查询最大ID -->
    <select id="queryMaxId" resultType="java.lang.Long">
        select COALESCE(max(cast(
            regexp_replace(fa_code, '^[A-Z]+', '', 'g') as bigint
        )), 0) as faCode
        from ams_property
        where type = #{type,jdbcType=VARCHAR}
          and hospital_id = #{hospitalId}
          and fa_code is not null
          and fa_code != ''
    </select>
    <!-- 查询在用资产的科室 -->
    <select id="queryUseDept" resultType="com.jp.med.common.vo.SelectOptionVo">
        select unnested.deptUseItem as value,
               y.org_name           as label,
               unnested.hospitalId
        from (select unnest(string_to_array(dept_use, ',')) as deptUseItem,
                     hospital_id                            as hospitalId
              from ams_property
              group by dept_use, hospital_id) unnested
                 inner join hrm_org y on
            unnested.deptUseItem = y.org_id and unnested.hospitalId = y.hospital_id
    </select>


    <select id="queryMaxSonFaCode" resultType="java.lang.Long">
        SELECT coalesce(MAX(CAST(split_part(fa_code, '-', -1) AS BIGINT)), 0) AS maxAfterHyphen
        FROM ams_property
        WHERE sa_code = #{saCode,jdbcType=VARCHAR}
          AND hospital_id = #{hospitalId};
    </select>
    <select id="queryParent" resultType="com.jp.med.ams.modules.property.dto.AmsPropertyDto">
        select fa_code,
               uid,
               asset_code,
               asset_type,
               asset_type_n
        from ams_property
        where fa_code =
              #{saCode}
          and hospital_id = #{hospitalId}
    </select>
    <select id="countByCategory" resultType="java.util.HashMap">
        SELECT COALESCE(t.asset_type_name,
                        tn.asset_type_name) as categoryname,
               COUNT(*)::varchar            as count
        FROM ams_property a
                 LEFT JOIN ams_type_cfg t ON a.asset_type = t.asset_type_code
                 LEFT JOIN ams_typen_cfg tn ON
            a.asset_type_n = tn.asset_type_code WHERE a.id IN
        <foreach collection="list" item="item"
                 separator="," open="(" close=")">
            #{item}
        </foreach>
        GROUP BY COALESCE(t.asset_type_name, tn.asset_type_name)
        ORDER BY count DESC
    </select>

    <select id="sumByCategory" resultType="java.util.HashMap">
        SELECT t.asset_type_name         as
                                            categoryname,
               sum(a.asset_nav)::varchar as count
        FROM ams_property a
                 LEFT JOIN ams_type_cfg t ON a.asset_type = t.asset_type_code WHERE a.id IN
        <foreach collection="list" item="item"
                 separator="," open="(" close=")">
            #{item}
        </foreach>
        GROUP BY t.asset_type_name
        ORDER BY count DESC
    </select>


    <!-- 按科室统计资产数量并按数量降序排序 -->
    <select id="topCountByDept" resultType="java.util.Map">
        WITH asset_depts AS (
        <!--    &#45;&#45; 有明确分配记录的资产-->
        SELECT a.id AS asset_id,
               asgn.org_id,
               asgn.prop
        FROM ams_property a
                 JOIN ams_depr_asgn asgn ON a.fa_code = asgn.fa_code AND asgn.active_flag = '1'
        <where>
            <if test="list != null and list.size() > 0">
                a.id IN
                <foreach collection="list" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>

        UNION ALL

        <!--    &#45;&#45; 无明确分配记录的资产（使用dept_use字段，默认比例为1）-->
        SELECT a.id      AS asset_id,
               dept_item AS org_id,
               1.0       AS prop
        FROM ams_property a
                 LEFT JOIN LATERAL unnest(string_to_array(a.dept_use, ',')) AS dept_item ON TRUE
        WHERE NOT EXISTS (SELECT 1
                          FROM ams_depr_asgn
                          WHERE fa_code = a.fa_code
                            AND active_flag = '1')
        <if test="list != null and list.size() > 0">
            AND a.id IN
            <foreach collection="list" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        )

        SELECT COALESCE(ho.org_name, '未分配') as deptName,
               CAST(SUM(ad.prop) AS varchar)   as count
        FROM asset_depts ad
                 LEFT JOIN hrm_org ho ON ad.org_id = ho.org_id
        GROUP BY ho.org_name
        ORDER BY SUM(ad.prop) DESC
    </select>


    <select id="queryAssetUseDept" resultType="java.lang.String">
        SELECT DISTINCT dept_use
        FROM ams_property WHERE fa_code IN
        <foreach collection="assetFaCodeList" item="item"
                 separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
</mapper>