# 🔧 EcsReimContractTask 添加 useOrg 字段方案

## 📋 需要修改的文件

### 1. 实体类 - EcsReimContractTask.java
**文件路径**: `med-common/src/main/java/com/jp/med/common/dto/ecs/EcsReimContractTask.java`

在第97行 `@TableField("bankcode")` 之后添加：

```java
@TableField("bankcode")
private String bankcode;

/** 🆕 使用科室 */
@TableField("use_org")
private String useOrg;
```

### 2. VO类 - EcsReimContractTaskVo.java
**文件路径**: `med-ecs/src/main/java/com/jp/med/ecs/modules/reimMgt/vo/EcsReimContractTaskVo.java`

在第62行 `private String appyerName;` 之后添加：

```java
/** 申请人姓名 **/
private String appyerName;

/** 🆕 使用科室 */
private String useOrg;
```

### 3. 查询SQL - EcsReimContractTaskReadMapper.xml
**文件路径**: `med-ecs/src/main/resources/mapper/reimMgt/read/EcsReimContractTaskReadMapper.xml`

在第30行的SELECT语句中添加 `use_org` 字段：

**修改前**:
```xml
,a.ct_name as ctName ,a.ct_unified_code as ctUnifiedCode ,a.need_reim_amt as needReimAmt
```

**修改后**:
```xml
,a.ct_name as ctName ,a.ct_unified_code as ctUnifiedCode ,a.need_reim_amt as needReimAmt
,a.use_org as useOrg
```

### 4. 后端服务 - CmsPaymentTermsWriteServiceImpl.java
**文件路径**: `med-cms/src/main/java/com/jp/med/cms/modules/contractManage/service/write/impl/CmsPaymentTermsWriteServiceImpl.java`

在 `goGenerateReim` 方法中添加 `useOrg` 字段的设置：

**在第177行 `taskDetail.setOrgId(appyerDept);` 之后添加**:
```java
// 报销科室
taskDetail.setOrgId(appyerDept);

// 🆕 设置使用科室
if (StringUtils.isNotEmpty(contractVo.getUseOrg())) {
    task.setUseOrg(contractVo.getUseOrg());
}
```

## 🗄️ 数据库更新SQL

### 1. 添加字段（如果还没有添加）
```sql
-- 为 ecs_reim_contract_task 表添加 use_org 字段
ALTER TABLE ecs_reim_contract_task 
ADD COLUMN use_org VARCHAR(50) COMMENT '使用科室';
```

### 2. 数据迁移（将现有数据的 appyer_dept 复制到 use_org）
```sql
-- 将现有的申请科室数据复制到使用科室字段
UPDATE ecs_reim_contract_task 
SET use_org = appyer_dept 
WHERE use_org IS NULL AND appyer_dept IS NOT NULL;
```

### 3. 添加索引（可选，提升查询性能）
```sql
-- 为 use_org 字段添加索引
CREATE INDEX idx_reim_task_use_org ON ecs_reim_contract_task(use_org);
```

## 🔄 前端传参确认

前端已经修改完成，会传递以下参数：
```javascript
const pushParams = {
  id: row.id,
  useOrg: props.contract.useOrg || null,
  useOrgName: props.contract.useOrgName || null,
  useOrgPerson: props.contract.useOrgPerson || null,
  useOrgPersonName: props.contract.useOrgPersonName || null,
  contractId: props.contract.id,
  ctCode: props.contract.ctCode,
  ctName: props.contract.ctName,
}
```

## ✅ 修改完成后的效果

1. **数据库**: `ecs_reim_contract_task` 表有 `use_org` 字段
2. **后端**: 实体类和VO类都包含 `useOrg` 属性
3. **接口**: 推送报销任务时会保存 `useOrg` 信息
4. **查询**: 查询报销任务时会返回 `useOrg` 信息

这样就完成了 `useOrg` 字段的完整集成！
